package com.mbh.blinddate.controller;

import com.mbh.blinddate.entity.UserSettings;
import com.mbh.blinddate.service.UserSettingsService;
import com.mbh.blinddate.util.CurrentContext;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/user/settings")
@RequiredArgsConstructor
public class UserSettingsController {
    
    private final UserSettingsService userSettingsService;
    
    /**
     * 获取用户设置
     */
    @GetMapping
    public ResponseEntity<UserSettings> getUserSettings() {
        Long userId = CurrentContext.getCurrentUserId();
        return ResponseEntity.ok(userSettingsService.getUserSettings(userId));
    }
    
    /**
     * 更新设置
     */
    @PutMapping
    public ResponseEntity<UserSettings> updateSettings(@Valid @RequestBody UserSettings settings) {
        Long userId = CurrentContext.getCurrentUserId();
        return ResponseEntity.ok(userSettingsService.updateSettings(userId, settings));
    }
    
    /**
     * 删除账号
     */
    @DeleteMapping("/account")
    public ResponseEntity<Void> deleteAccount(@RequestParam String password) {
        Long userId = CurrentContext.getCurrentUserId();
        userSettingsService.deleteAccount(userId, password);
        return ResponseEntity.ok().build();
    }
}
