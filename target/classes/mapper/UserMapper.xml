<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mbh.blinddate.mapper.UserMapper">
    <resultMap id="userMap" type="com.mbh.blinddate.entity.User">
        <id property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="birthday" column="birthday"/>
        <result property="location" column="location"/>
        <result property="bio" column="bio"/>
        <result property="lastLoginTime" column="last_login_time"/>
        <result property="lastLoginIp" column="last_login_ip"/>
        <result property="loginCount" column="login_count"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <select id="findById" resultType="com.mbh.blinddate.entity.User">
        SELECT * FROM users WHERE id = #{id}
    </select>

    <select id="findByUsername" resultType="com.mbh.blinddate.entity.User">
        SELECT * FROM users WHERE username = #{username}
    </select>

    <select id="findByEmail" resultType="com.mbh.blinddate.entity.User">
        SELECT * FROM users WHERE email = #{email}
    </select>

    <select id="findByPhone" resultType="com.mbh.blinddate.entity.User">
        SELECT * FROM users WHERE phone = #{phone}
    </select>

    <select id="existsByUsername" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE username = #{username}
    </select>

    <select id="existsByEmail" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE email = #{email}
    </select>

    <select id="existsByPhone" resultType="boolean">
        SELECT COUNT(*) > 0 FROM users WHERE phone = #{phone}
    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (username, password, email, phone)
        VALUES (#{username}, #{password}, #{email}, #{phone})
    </insert>

    <update id="update">
        UPDATE users
        <set>
            <if test="password != null">password = #{password},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            updated_at = CURRENT_TIMESTAMP
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById">
        DELETE FROM users WHERE id = #{id}
    </delete>
</mapper>
